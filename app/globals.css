@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  /* Anthropic-inspired color system for light theme */
  --background: #ffffff;
  --foreground: #1f2937;
  --card: #ffffff;
  --card-foreground: #1f2937;
  --popover: #ffffff;
  --popover-foreground: #1f2937;
  --primary: #1f2937;
  --primary-foreground: #ffffff;
  --secondary: #f8f9fa;
  --secondary-foreground: #1f2937;
  --muted: #f8f9fa;
  --muted-foreground: #6b7280;
  --accent: #d97756;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #d97756;
  --chart-1: #d97756;
  --chart-2: #6b7280;
  --chart-3: #1f2937;
  --chart-4: #f8f9fa;
  --chart-5: #e5e7eb;
  --radius: 0.5rem;
  --sidebar: #ffffff;
  --sidebar-foreground: #1f2937;
  --sidebar-primary: #1f2937;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f8f9fa;
  --sidebar-accent-foreground: #1f2937;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #d97756;
}

.dark {
  /* Anthropic-inspired color system for dark theme */
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #f1f5f9;
  --primary-foreground: #0f172a;
  --secondary: #1e293b;
  --secondary-foreground: #f1f5f9;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #d97756;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #334155;
  --input: #334155;
  --ring: #d97756;
  --chart-1: #d97756;
  --chart-2: #94a3b8;
  --chart-3: #f1f5f9;
  --chart-4: #1e293b;
  --chart-5: #334155;
  --sidebar: #0f172a;
  --sidebar-foreground: #f1f5f9;
  --sidebar-primary: #d97756;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #1e293b;
  --sidebar-accent-foreground: #f1f5f9;
  --sidebar-border: #334155;
  --sidebar-ring: #d97756;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Custom font family variables */
  --font-sans: var(--font-inter);
  --font-serif: Georgia, "Times New Roman", Times, serif;
  --font-mono: var(--font-jetbrains-mono);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
    /* Added smooth transitions for theme switching */
    transition: background-color 0.3s ease, color 0.3s ease;
  }

  /* Typography styles for Anthropic-inspired design */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-sans font-semibold tracking-tight;
  }

  .prose {
    @apply font-serif;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    @apply font-sans;
  }

  code {
    @apply font-mono;
  }

  /* Added smooth hover transitions for interactive elements */
  a,
  button {
    transition: color 0.2s ease, background-color 0.2s ease, transform 0.1s ease;
  }

  /* Smooth scroll behavior */
  html {
    scroll-behavior: smooth;
  }
}
