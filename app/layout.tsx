import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import { JetBrains_Mono } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Navigation } from "@/components/navigation"
import { Footer } from "@/components/footer"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-inter",
})

const jetbrainsMono = JetBrains_Mono({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-jetbrains-mono",
})

export const metadata: Metadata = {
  title: "Personal Thoughts - Curiosity-Driven Development",
  description: "A space for sharing thoughts, insights, and learnings from the journey in tech",
  generator: "v0.dev",
  keywords: ["web development", "react", "typescript", "software engineering", "tech blog"],
  authors: [{ name: "Curious Dev" }],
  creator: "Curious Dev",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://curiousdev.com",
    title: "Personal Thoughts - Curiosity-Driven Development",
    description: "A space for sharing thoughts, insights, and learnings from the journey in tech",
    siteName: "Curious Dev",
  },
  twitter: {
    card: "summary_large_image",
    title: "Personal Thoughts - Curiosity-Driven Development",
    description: "A space for sharing thoughts, insights, and learnings from the journey in tech",
    creator: "@curiousdev",
  },
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${inter.variable} ${jetbrainsMono.variable} antialiased`} suppressHydrationWarning>
      <body>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <div className="flex min-h-screen flex-col">
            {/* Fixed navigation layout to properly integrate mobile and desktop */}
            <Navigation />

            {/* Main content area with proper spacing */}
            <main className="flex-1">{children}</main>

            {/* Footer */}
            <Footer />
          </div>
        </ThemeProvider>
      </body>
    </html>
  )
}
