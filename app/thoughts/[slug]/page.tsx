import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, Calendar, Clock, User, Share2, BookO<PERSON> } from "lucide-react"
import Link from "next/link"

// This would typically come from a CMS or markdown files
const getBlogPost = (slug: string) => {
  const posts = {
    "building-resilient-systems": {
      title: "Building Resilient Systems",
      excerpt: "Exploring patterns for creating software that gracefully handles uncertainty and change.",
      category: "Technical Deep-Dive",
      readTime: "8 min read",
      date: "2024-01-15",
      author: "Curious Dev",
      content: `
# Building Resilient Systems

In the ever-evolving landscape of software development, one truth remains constant: systems will face unexpected challenges. Whether it's a sudden spike in traffic, a third-party service going down, or simply the unpredictable nature of user behavior, our applications must be prepared to handle uncertainty with grace.

## What Makes a System Resilient?

Resilience isn't just about staying online when things go wrong—it's about maintaining functionality, user experience, and data integrity under stress. A truly resilient system exhibits several key characteristics:

### 1. Graceful Degradation

When a non-critical service fails, the system should continue to operate with reduced functionality rather than failing completely. Think of how Netflix continues to work even when their recommendation engine is down—you can still browse and watch content.

### 2. Circuit Breakers

Implementing circuit breaker patterns prevents cascading failures. When a service starts failing, the circuit breaker "opens" and stops sending requests to the failing service, giving it time to recover.

\`\`\`typescript
class CircuitBreaker {
  private failureCount = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  async call<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }
}
\`\`\`

### 3. Retry with Backoff

Not all failures are permanent. Implementing intelligent retry mechanisms with exponential backoff can help systems recover from temporary issues without overwhelming failing services.

## The Human Element

Building resilient systems isn't just about code—it's about understanding human behavior and designing for real-world scenarios. Users don't always follow the happy path, and that's okay. Our job is to guide them back to success, even when things go wrong.

## Conclusion

Resilience is a journey, not a destination. Every system failure is an opportunity to learn and improve. By embracing uncertainty and designing for failure, we create software that not only survives but thrives in the face of adversity.

The next time you're designing a system, ask yourself: "What happens when this fails?" The answer to that question will guide you toward building something truly resilient.
      `,
    },
  }

  return posts[slug as keyof typeof posts] || null
}

export default function BlogPostPage({ params }: { params: { slug: string } }) {
  const post = getBlogPost(params.slug)

  if (!post) {
    return (
      <div className="container mx-auto px-4 py-16 text-center">
        <h1 className="text-2xl font-bold mb-4">Post Not Found</h1>
        <p className="text-muted-foreground mb-8">The post you're looking for doesn't exist.</p>
        <Link href="/thoughts">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Thoughts
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="bg-background">
      {/* Back Navigation */}
      <section className="container mx-auto px-4 py-8">
        <Link href="/thoughts">
          <Button variant="ghost" className="mb-8">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Thoughts
          </Button>
        </Link>
      </section>

      {/* Article Header */}
      <article className="container mx-auto px-4 pb-16">
        <div className="max-w-4xl mx-auto">
          <header className="mb-12">
            <Badge variant="secondary" className="mb-4">
              {post.category}
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">{post.title}</h1>
            <p className="text-xl text-muted-foreground mb-8 font-serif leading-relaxed">{post.excerpt}</p>

            <div className="flex items-center justify-between border-b pb-8">
              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>{post.author}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(post.date).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>{post.readTime}</span>
                </div>
              </div>
              <Button variant="outline" size="sm">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </header>

          {/* Article Content */}
          <div className="prose prose-lg max-w-none font-serif">
            <div dangerouslySetInnerHTML={{ __html: post.content.replace(/\n/g, "<br />") }} />
          </div>

          {/* Article Footer */}
          <footer className="mt-16 pt-8 border-t">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-accent/10 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-accent" />
                </div>
                <div>
                  <p className="font-semibold">{post.author}</p>
                  <p className="text-sm text-muted-foreground">Curious developer sharing the journey</p>
                </div>
              </div>
              <Button variant="outline">
                <BookOpen className="h-4 w-4 mr-2" />
                More Posts
              </Button>
            </div>
          </footer>
        </div>
      </article>
    </div>
  )
}
