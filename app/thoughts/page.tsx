import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { ArrowRight, BookOpen, Calendar, Clock, Coffee, Lightbulb, Search } from "lucide-react"

// Sample blog posts data
const blogPosts = [
  {
    id: "building-resilient-systems",
    title: "Building Resilient Systems",
    excerpt: "Exploring patterns for creating software that gracefully handles uncertainty and change.",
    category: "Technical Deep-Dive",
    categoryIcon: Lightbulb,
    readTime: "8 min read",
    date: "2024-01-15",
    author: "Curious Dev",
  },
  {
    id: "art-of-code-review",
    title: "The Art of Code Review",
    excerpt: "Lessons learned from thousands of code reviews and how they shape better software.",
    category: "Learning Log",
    categoryIcon: BookOpen,
    readTime: "6 min read",
    date: "2024-01-10",
    author: "Curious Dev",
  },
  {
    id: "curiosity-and-growth",
    title: "On Curiosity & Growth",
    excerpt: "Why staying curious is the most important skill for any developer in 2024.",
    category: "Reflection",
    categoryIcon: Coffee,
    readTime: "4 min read",
    date: "2024-01-05",
    author: "Curious Dev",
  },
  {
    id: "modern-react-patterns",
    title: "Modern React Patterns That Actually Matter",
    excerpt: "A deep dive into React patterns that solve real problems, not just showcase clever code.",
    category: "Technical Deep-Dive",
    categoryIcon: Lightbulb,
    readTime: "12 min read",
    date: "2024-01-01",
    author: "Curious Dev",
  },
  {
    id: "learning-in-public",
    title: "Learning in Public: A Year Later",
    excerpt: "Reflections on a year of sharing my learning journey and the unexpected benefits it brought.",
    category: "Learning Log",
    categoryIcon: BookOpen,
    readTime: "7 min read",
    date: "2023-12-28",
    author: "Curious Dev",
  },
  {
    id: "developer-empathy",
    title: "The Developer's Guide to Empathy",
    excerpt: "How understanding users, teammates, and even your future self makes you a better developer.",
    category: "Reflection",
    categoryIcon: Coffee,
    readTime: "5 min read",
    date: "2023-12-20",
    author: "Curious Dev",
  },
]

const categories = ["All", "Technical Deep-Dive", "Learning Log", "Reflection", "Industry Thoughts"]

export default function ThoughtsPage() {
  return (
    <div className="bg-background">
      {/* Header */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Thoughts & Insights</h1>
          <p className="text-xl text-muted-foreground mb-8 font-serif leading-relaxed">
            A collection of thoughts, learnings, and discoveries from my journey in tech. Each post is an exploration, a
            question, or a reflection on the craft of building software.
          </p>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="container mx-auto px-4 pb-8">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search thoughts..." className="pl-10" />
            </div>
            <div className="flex gap-2 flex-wrap">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={category === "All" ? "default" : "outline"}
                  size="sm"
                  className={category === "All" ? "bg-accent hover:bg-accent/90" : ""}
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Blog Posts Grid */}
      <section className="container mx-auto px-4 pb-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {blogPosts.map((post) => {
              const IconComponent = post.categoryIcon
              return (
                <Card key={post.id} className="hover:shadow-lg transition-all duration-300 group">
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-3">
                      <IconComponent className="h-4 w-4 text-accent" />
                      <Badge variant="secondary" className="text-xs">
                        {post.category}
                      </Badge>
                    </div>
                    <CardTitle className="group-hover:text-accent transition-colors">{post.title}</CardTitle>
                    <CardDescription className="font-serif leading-relaxed">{post.excerpt}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(post.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{post.readTime}</span>
                        </div>
                      </div>
                    </div>
                    <Button variant="ghost" className="p-0 h-auto group-hover:text-accent transition-colors">
                      Read more <ArrowRight className="ml-1 h-3 w-3" />
                    </Button>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="container mx-auto px-4 py-16">
        <div className="max-w-4xl mx-auto">
          <Card className="bg-secondary/50">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl mb-2">Stay Curious</CardTitle>
              <CardDescription className="text-lg font-serif">
                Get notified when I publish new thoughts and insights. No spam, just genuine curiosity shared.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <Input placeholder="Enter your email" className="flex-1" />
                <Button className="bg-accent hover:bg-accent/90">Subscribe</Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}
