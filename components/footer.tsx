import Link from "next/link"
import { Github, Twitter, Linkedin, Mail } from "lucide-react"

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Curious Dev</h3>
            <p className="text-sm text-muted-foreground font-serif">
              Exploring the intersection of technology, curiosity, and human-centered design.
            </p>
          </div>

          {/* Navigation */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Navigate</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/thoughts" className="text-muted-foreground hover:text-accent transition-colors">
                  Thoughts
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-accent transition-colors">
                  About
                </Link>
              </li>
              <li>
                <Link href="/now" className="text-muted-foreground hover:text-accent transition-colors">
                  Now
                </Link>
              </li>
              <li>
                <Link href="/links" className="text-muted-foreground hover:text-accent transition-colors">
                  Links
                </Link>
              </li>
            </ul>
          </div>

          {/* Topics */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Topics</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link
                  href="/thoughts?category=technical"
                  className="text-muted-foreground hover:text-accent transition-colors"
                >
                  Technical Deep-Dives
                </Link>
              </li>
              <li>
                <Link
                  href="/thoughts?category=learning"
                  className="text-muted-foreground hover:text-accent transition-colors"
                >
                  Learning Logs
                </Link>
              </li>
              <li>
                <Link
                  href="/thoughts?category=industry"
                  className="text-muted-foreground hover:text-accent transition-colors"
                >
                  Industry Thoughts
                </Link>
              </li>
              <li>
                <Link
                  href="/thoughts?category=reflection"
                  className="text-muted-foreground hover:text-accent transition-colors"
                >
                  Reflections
                </Link>
              </li>
            </ul>
          </div>

          {/* Connect */}
          <div className="space-y-4">
            <h4 className="text-sm font-semibold">Connect</h4>
            <div className="flex space-x-4">
              <Link href="#" className="text-muted-foreground hover:text-accent transition-colors">
                <Github className="h-5 w-5" />
                <span className="sr-only">GitHub</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-accent transition-colors">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-accent transition-colors">
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </Link>
              <Link href="#" className="text-muted-foreground hover:text-accent transition-colors">
                <Mail className="h-5 w-5" />
                <span className="sr-only">Email</span>
              </Link>
            </div>
          </div>
        </div>

        <div className="mt-8 pt-8 border-t text-center text-sm text-muted-foreground">
          <p>&copy; 2024 Curious Dev. Built with curiosity and care.</p>
        </div>
      </div>
    </footer>
  )
}
